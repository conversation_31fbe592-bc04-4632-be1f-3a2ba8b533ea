# Product Overview

ABM-LLM is an intelligent agent dialogue simulation platform that combines the capabilities of Large Language Models (LLMs) with traditional Agent-Based Modeling (ABM) advantages.

## Core Innovation

The platform focuses on simulating dialogue-based intelligent agent interactions with a dual-engine architecture:

- **Natural Language Rules Engine**: Handles complex semantics and fuzzy conditions
- **Logic Rules Engine**: Processes precise calculations and deterministic logic
- **Supervisor Mechanism**: Built-in supervisor roles for automatic monitoring and intervention
- **MCP Plugin System**: Extends beyond dialogue to support agent control and actions via Model-Control-Protocol plugins

## Key Differentiators

Unlike traditional ABM software (NetLogo/Mesa/AnyLogic) that focuses on spatial movement and state changes, this system emphasizes:
- Dialogue and communication as the primary interaction mode
- Dual-engine rule system supporting both natural language and programmatic rules
- Lower barrier to entry for non-technical users
- Complex multi-party interaction scenarios like expert meetings, team collaboration, and decision-making processes

## Target Scenarios

- Enterprise decision-making and management
- Medical consultations with multiple experts
- Educational training and debate simulations
- Policy analysis and multi-party negotiations
- R&D collaboration and innovation discussions
- Smart system control through MCP plugins
- Automated workflows where agents execute real actions based on dialogue outcomes