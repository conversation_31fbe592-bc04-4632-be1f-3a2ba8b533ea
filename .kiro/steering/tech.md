# Technology Stack

## Architecture

Full-stack web application with separate backend and frontend services:

- **Backend**: Python + Flask (REST API and WebSocket support)
- **Frontend**: React + Ant Design UI components
- **Database**: SQLite (development) with SQLAlchemy ORM
- **Package Management**: pnpm for frontend, pip/conda for backend

## Backend Technologies

### Core Framework
- **Flask 3.1.0**: Web framework with Flask-CORS, Flask-SocketIO, Flask-SQLAlchemy
- **SQLAlchemy 2.0.39**: ORM with Alembic for migrations
- **Redis 5.2.1**: Caching and session management
- **EventLet 0.39.1**: Async/WebSocket support

### AI/ML Integration
- **MCP (Model Context Protocol) 1.6.0**: Plugin system for external tool integration
- **sentence-transformers 3.3.1**: Text embeddings
- **TiDB Vector 0.0.9**: Vector database support
- **PyMilvus 2.3.4**: Alternative vector database

### API & Documentation
- **flask-restx**: API documentation and validation
- **marshmallow**: Data serialization
- **apispec**: OpenAPI specification

## Frontend Technologies

### Core Framework
- **React 19.1.0**: UI framework
- **Ant Design 5.26.6**: Component library
- **React Router DOM 7.7.1**: Client-side routing

### Development Tools
- **CRACO 7.1.0**: Create React App Configuration Override
- **Monaco Editor**: Code editing capabilities
- **CodeMirror**: Syntax highlighting for multiple languages

### Visualization & UI
- **ECharts 5.6.0**: Data visualization
- **Mermaid 11.9.0**: Diagram rendering
- **React Markdown 10.1.0**: Markdown rendering with KaTeX math support

## Common Commands

### Backend Development
```bash
# Environment setup
source /opt/homebrew/Caskroom/miniconda/base/etc/profile.d/conda.sh && conda activate abm

# Start backend server
python run_app.py

# Database migrations
flask db upgrade

# Run tests
python -m pytest backend/tests/
```

### Frontend Development
```bash
# Install dependencies
cd frontend && pnpm install

# Start development server
npm start
# or
npm run dev

# Build for production
npm run build

# Run tests
npm test
```

### Docker Deployment
```bash
# Build and run with Docker Compose
cd docker
docker-compose up -d

# View logs
docker-compose logs -f abm-app
```

## Configuration

### Environment Variables
- Backend configuration in `backend/config.py` and `backend/config.conf`
- Frontend proxy configuration in `frontend/setupProxy.js`
- MCP server configuration in `backend/mcp_config.json`

### Default Ports
- Backend API: http://localhost:8080
- Frontend: http://localhost:3000
- Neo4j (if using Graphiti): bolt://localhost:7687

## Development Workflow

1. Backend runs on port 8080 with Flask development server
2. Frontend runs on port 3000 with React development server
3. Frontend proxies API requests to backend via setupProxy.js
4. WebSocket connections handled through Flask-SocketIO
5. Database migrations managed through Alembic