# Project Structure

## Root Directory Organization

```
abm-llm-v2/
├── backend/           # Python Flask backend service
├── frontend/          # React frontend application
├── docker/            # Docker configuration and deployment
├── docs/              # Documentation and technical guides
├── tests/             # Integration and system tests
├── tools/             # License validation and utility tools
├── thirt_party/       # Third-party integrations (Graphiti)
├── examples/          # Usage examples and demos
└── logs/              # Application logs
```

## Backend Structure (`backend/`)

### Core Application (`backend/app/`)
```
app/
├── __init__.py                    # Flask app factory
├── models.py                      # SQLAlchemy database models
├── extensions.py                  # Flask extensions initialization
├── api/                          # API route definitions
├── controllers/                  # Business logic controllers
├── services/                     # Core business services
├── middleware/                   # Request/response middleware
├── utils/                        # Utility functions and helpers
├── static/                       # Static files
└── seed_data_*.json              # Database seed data files
```

### Key Service Modules
- `services/conversation/`: Message processing, model clients, conversation management
- `services/vector_db/`: Vector database integration (TiDB, Milvus)
- `services/mcp_servers/`: MCP (Model Context Protocol) server management
- `controllers/routes.py`: Main route registration
- `models/`: Database models for agents, conversations, rules, etc.

### Configuration & Data
- `config.py` & `config.conf`: Application configuration
- `run_app.py`: Application entry point
- `requirements.txt`: Python dependencies
- `migrations/`: Database migration scripts
- `agent-workspace/`: Agent execution workspaces
- `knowledgebase/`: Knowledge base storage

## Frontend Structure (`frontend/`)

### Source Code (`frontend/src/`)
```
src/
├── App.js                        # Main application component
├── index.js                      # React application entry point
├── components/                   # Reusable UI components
├── pages/                        # Page-level components
├── contexts/                     # React context providers
├── hooks/                        # Custom React hooks
├── services/                     # API service functions
├── utils/                        # Frontend utility functions
└── setupProxy.js                 # Development proxy configuration
```

### Build Configuration
- `package.json`: Dependencies and scripts
- `craco.config.js`: Create React App configuration override
- `webpack.config.js`: Custom webpack configuration
- `public/`: Static assets and HTML template

## Documentation (`docs/`)

### Key Documentation Files
- `README.md`: Project overview and setup instructions
- `arch.md`: System architecture documentation
- `API.md`: API documentation
- `FEATURES.md`: Feature specifications
- `PLAN-*.md`: Development planning documents
- `*_INTEGRATION_*.md`: Integration guides (Graphiti, TiDB, etc.)

## Development Conventions

### File Naming
- **Python**: snake_case for files and functions
- **JavaScript/React**: camelCase for functions, PascalCase for components
- **Configuration**: lowercase with extensions (.conf, .json, .yml)

### Directory Structure Patterns
- Services are organized by domain (conversation, vector_db, mcp_servers)
- API routes follow RESTful conventions
- Frontend components are organized by feature/page
- Tests mirror the source code structure

### Import Conventions
- Use relative imports within the same module
- Use absolute imports from app root (`from app.services...`)
- Frontend uses ES6 import/export syntax

### Database Models
- Located in `backend/app/models.py` and `backend/app/models/`
- Use SQLAlchemy ORM with declarative base
- Migration scripts in `backend/migrations/`

### Configuration Management
- Environment-specific configs in `backend/config.py`
- Runtime configuration in `backend/config.conf`
- Frontend environment variables in `.env` files
- MCP server configuration in `backend/mcp_config.json`

## Key Integration Points

### MCP (Model Context Protocol)
- Server configurations in `backend/mcp_config.json`
- Service implementations in `backend/app/mcp_servers/`
- Plugin system for external tool integration

### Vector Databases
- TiDB Vector and Milvus support in `backend/app/services/vector_db/`
- Embedding services for text processing
- Knowledge base integration

### Third-Party Services
- Graphiti integration in `thirt_party/graphiti/`
- External knowledge bases (Dify, RagFlow, MaxKB)
- Multi-modal services (FastGPT)

### Agent Workspaces
- Individual agent execution environments in `backend/agent-workspace/`
- Action task management and execution
- Sandbox environments for agent operations