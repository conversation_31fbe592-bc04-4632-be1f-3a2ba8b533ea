version: '3.8'

services:
  neo4j:
    image: neo4j:5.26
    container_name: abm-neo4j
    ports:
      - "7474:7474"  # HTTP
      - "7687:7687"  # Bolt
    environment:
      - NEO4J_AUTH=neo4j/password
      - NEO4J_PLUGINS=["apoc"]
      - NEO4J_dbms_security_procedures_unrestricted=apoc.*
      - NEO4J_dbms_security_procedures_allowlist=apoc.*
    volumes:
      - neo4j_data:/data
      - neo4j_logs:/logs
      - neo4j_import:/var/lib/neo4j/import
      - neo4j_plugins:/plugins
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "cypher-shell -u neo4j -p password 'RETURN 1'"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s

  # 可选：FalkorDB作为轻量级替代方案
  falkordb:
    image: falkordb/falkordb:latest
    container_name: abm-falkordb
    ports:
      - "6379:6379"  # Redis protocol
      - "3000:3000"  # Web UI
    volumes:
      - falkordb_data:/data
    restart: unless-stopped
    profiles:
      - falkordb  # 使用 --profile falkordb 启动

volumes:
  neo4j_data:
  neo4j_logs:
  neo4j_import:
  neo4j_plugins:
  falkordb_data:
