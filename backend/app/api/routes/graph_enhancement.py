"""
图谱增强API路由

处理与图谱增强相关的所有API请求，包括：
- 配置管理（增删改查）
- 状态查询
- 测试查询
- 框架集成
"""

import os
import json
import uuid
from datetime import datetime
from flask import Blueprint, request, jsonify, current_app
from sqlalchemy.exc import IntegrityError

from app.models import GraphEnhancement, db
from app.services.graph_enhancement_service import GraphEnhancementService

# 创建Blueprint
graph_enhancement_bp = Blueprint('graph_enhancement_api', __name__)

# 图谱增强服务实例
graph_service = GraphEnhancementService()

# ==================== 配置管理接口 ====================

@graph_enhancement_bp.route('/graph-enhancement/config', methods=['GET'])
def get_graph_enhancement_config():
    """获取图谱增强配置"""
    try:
        # 获取或创建默认配置
        config = GraphEnhancement.query.first()
        if not config:
            # 创建默认配置
            config = GraphEnhancement(
                name='默认图谱增强配置',
                description='系统默认的图谱增强配置',
                enabled=False,
                framework='lightrag'
            )
            db.session.add(config)
            db.session.commit()
        
        return jsonify({
            'success': True,
            'data': {
                'id': config.id,
                'enabled': config.enabled,
                'framework': config.framework,
                'name': config.name,
                'description': config.description,
                'framework_config': config.framework_config or {},
                'created_at': config.created_at.isoformat() if config.created_at else None,
                'updated_at': config.updated_at.isoformat() if config.updated_at else None
            }
        })
        
    except Exception as e:
        current_app.logger.error(f"获取图谱增强配置失败: {e}")
        return jsonify({
            'success': False,
            'message': f'获取图谱增强配置失败: {str(e)}'
        }), 500

@graph_enhancement_bp.route('/graph-enhancement/config', methods=['POST'])
def update_graph_enhancement_config():
    """更新图谱增强配置"""
    try:
        data = request.get_json()
        
        # 获取或创建配置
        config = GraphEnhancement.query.first()
        if not config:
            config = GraphEnhancement(
                name='默认图谱增强配置',
                description='系统默认的图谱增强配置'
            )
            db.session.add(config)
        
        # 更新基础配置字段
        if 'enabled' in data:
            config.enabled = data['enabled']
        if 'framework' in data:
            config.framework = data['framework']
        if 'name' in data:
            config.name = data['name']
        if 'description' in data:
            config.description = data['description']

        # 更新框架配置 - 所有其他配置都存储在这里
        if 'framework_config' in data:
            config.framework_config = data['framework_config']
            current_app.logger.info(f"保存框架配置: {data['framework_config']}")
        
        db.session.commit()
        
        # 如果启用了图谱增强，初始化服务
        if config.enabled:
            try:
                success, message = graph_service.initialize_framework(config)
                if not success:
                    return jsonify({
                        'success': False,
                        'message': f'初始化图谱增强框架失败: {message}'
                    }), 500
            except Exception as e:
                current_app.logger.error(f"初始化图谱增强框架异常: {e}")
                return jsonify({
                    'success': False,
                    'message': f'初始化图谱增强框架异常: {str(e)}'
                }), 500
        
        return jsonify({
            'success': True,
            'message': '图谱增强配置更新成功',
            'data': {
                'id': config.id,
                'enabled': config.enabled,
                'framework': config.framework
            }
        })
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"更新图谱增强配置失败: {e}")
        return jsonify({
            'success': False,
            'message': f'更新图谱增强配置失败: {str(e)}'
        }), 500

# ==================== 状态查询接口 ====================

@graph_enhancement_bp.route('/graph-enhancement/status', methods=['GET'])
def get_graph_enhancement_status():
    """获取图谱增强状态"""
    try:
        config = GraphEnhancement.query.first()
        if not config or not config.enabled:
            return jsonify({
                'success': True,
                'data': {
                    'enabled': False,
                    'status': 'disabled',
                    'message': '图谱增强未启用'
                }
            })
        
        # 获取框架状态
        status_info = graph_service.get_status(config)
        
        return jsonify({
            'success': True,
            'data': status_info
        })
        
    except Exception as e:
        current_app.logger.error(f"获取图谱增强状态失败: {e}")
        return jsonify({
            'success': False,
            'message': f'获取图谱增强状态失败: {str(e)}'
        }), 500

# ==================== 测试查询接口 ====================

@graph_enhancement_bp.route('/graph-enhancement/test-query', methods=['POST'])
def test_graph_enhancement_query():
    """测试图谱增强查询"""
    try:
        data = request.get_json()

        query = data.get('query', '')
        if not query:
            return jsonify({
                'success': False,
                'message': '查询内容不能为空'
            }), 400

        config = GraphEnhancement.query.first()
        if not config or not config.enabled:
            return jsonify({
                'success': False,
                'message': '图谱增强未启用'
            }), 400

        # 根据框架类型构建查询参数
        if config.framework == 'graphiti':
            # Graphiti 框架参数
            query_params = {
                'max_facts': data.get('max_facts', 10),
                'group_ids': data.get('group_ids', [])
            }
        else:
            # LightRAG 等其他框架参数
            query_params = {
                'mode': data.get('mode', config.default_query_mode),
                'top_k': data.get('top_k', config.top_k),
                'chunk_top_k': data.get('chunk_top_k', config.chunk_top_k),
                'response_type': data.get('response_type', 'Multiple Paragraphs')
            }

        start_time = datetime.now()
        success, result = graph_service.query(config, query, query_params)
        end_time = datetime.now()

        response_time = (end_time - start_time).total_seconds()

        if success:
            return jsonify({
                'success': True,
                'data': {
                    'query': query,
                    'result': result,
                    'response_time': response_time,
                    'query_params': query_params,
                    'framework': config.framework
                }
            })
        else:
            return jsonify({
                'success': False,
                'message': f'查询失败: {result}'
            }), 500

    except Exception as e:
        current_app.logger.error(f"测试图谱增强查询失败: {e}")
        return jsonify({
            'success': False,
            'message': f'测试图谱增强查询失败: {str(e)}'
        }), 500


@graph_enhancement_bp.route('/graph-enhancement/test-advanced-query', methods=['POST'])
def test_advanced_graph_enhancement_query():
    """高级图谱增强查询测试"""
    try:
        data = request.get_json()

        query = data.get('query', '')
        if not query:
            return jsonify({
                'success': False,
                'message': '查询内容不能为空'
            }), 400

        config = GraphEnhancement.query.first()
        if not config or not config.enabled:
            return jsonify({
                'success': False,
                'message': '图谱增强未启用'
            }), 400

        # 构建高级查询参数
        if config.framework == 'graphiti':
            query_params = {
                'query': query,
                'search_mode': data.get('search_mode', 'cross_encoder'),
                'max_facts': data.get('max_facts', 15),
                'group_ids': data.get('group_ids', []),
                'reranker_min_score': data.get('reranker_min_score', 0.6),
                'sim_min_score': data.get('sim_min_score', 0.5),
                'enable_filters': data.get('enable_filters', False),
                'node_labels': data.get('node_labels', []),
                'edge_types': data.get('edge_types', [])
            }
        else:
            # 其他框架保持原有逻辑
            query_params = {
                'mode': data.get('mode', config.default_query_mode),
                'top_k': data.get('top_k', config.top_k),
                'chunk_top_k': data.get('chunk_top_k', config.chunk_top_k),
                'response_type': data.get('response_type', 'Multiple Paragraphs')
            }

        start_time = datetime.now()
        success, result = graph_service.query_advanced(config, query, query_params)
        end_time = datetime.now()

        response_time = (end_time - start_time).total_seconds()

        if success:
            # 处理结构化结果
            if isinstance(result, dict) and 'type' in result:
                # 结构化结果
                return jsonify({
                    'success': True,
                    'data': {
                        'query': query,
                        'result': result,
                        'response_time': response_time,
                        'search_config': query_params.get('search_strategy', 'default'),
                        'query_type': query_params.get('query_type', 'general'),
                        'total_results': result.get('total_count', 0),
                        'config_description': f"使用{query_params.get('search_strategy', 'default')}策略进行{query_params.get('query_type', 'general')}查询",
                        'framework': config.framework
                    }
                })
            else:
                # 传统文本结果
                return jsonify({
                    'success': True,
                    'data': {
                        'query': query,
                        'result': result,
                        'response_time': response_time,
                        'search_config': query_params.get('search_strategy', 'default'),
                        'query_type': query_params.get('query_type', 'general'),
                        'total_results': len(result.split('\n\n')) if isinstance(result, str) else 0,
                        'config_description': f"使用{query_params.get('search_strategy', 'default')}策略进行{query_params.get('query_type', 'general')}查询",
                        'framework': config.framework
                    }
                })
        else:
            return jsonify({
                'success': False,
                'message': f'查询失败: {result}'
            }), 500

    except Exception as e:
        current_app.logger.error(f"高级图谱增强查询测试失败: {e}")
        return jsonify({
            'success': False,
            'message': f'高级图谱增强查询测试失败: {str(e)}'
        }), 500


# ==================== 数据管理接口 ====================

@graph_enhancement_bp.route('/graph-enhancement/rebuild-index', methods=['POST'])
def rebuild_graph_enhancement_index():
    """重建图谱增强索引"""
    try:
        config = GraphEnhancement.query.first()
        if not config or not config.enabled:
            return jsonify({
                'success': False,
                'message': '图谱增强未启用'
            }), 400
        
        # 重建索引
        success, message = graph_service.rebuild_index(config)
        
        return jsonify({
            'success': success,
            'message': message
        })
        
    except Exception as e:
        current_app.logger.error(f"重建图谱增强索引失败: {e}")
        return jsonify({
            'success': False,
            'message': f'重建图谱增强索引失败: {str(e)}'
        }), 500

@graph_enhancement_bp.route('/graph-enhancement/clear-graph', methods=['POST'])
def clear_graph_enhancement_data():
    """清空图谱增强数据"""
    try:
        config = GraphEnhancement.query.first()
        if not config or not config.enabled:
            return jsonify({
                'success': False,
                'message': '图谱增强未启用'
            }), 400

        # 清空数据
        success, message = graph_service.clear_data(config)

        return jsonify({
            'success': success,
            'message': message
        })

    except Exception as e:
        current_app.logger.error(f"清空图谱增强数据失败: {e}")
        return jsonify({
            'success': False,
            'message': f'清空图谱增强数据失败: {str(e)}'
        }), 500

@graph_enhancement_bp.route('/graph-enhancement/add-documents', methods=['POST'])
def add_documents_to_graph():
    """添加文档到图谱增强系统"""
    try:
        data = request.get_json()

        documents = data.get('documents', [])
        if not documents:
            return jsonify({
                'success': False,
                'message': '文档列表不能为空'
            }), 400

        config = GraphEnhancement.query.first()
        if not config or not config.enabled:
            return jsonify({
                'success': False,
                'message': '图谱增强未启用'
            }), 400

        # 添加文档
        success, message = graph_service.add_documents(config, documents)

        return jsonify({
            'success': success,
            'message': message,
            'document_count': len(documents)
        })

    except Exception as e:
        current_app.logger.error(f"添加文档到图谱增强系统失败: {e}")
        return jsonify({
            'success': False,
            'message': f'添加文档失败: {str(e)}'
        }), 500

@graph_enhancement_bp.route('/graph-enhancement/service-control', methods=['POST'])
def control_graphiti_service():
    """控制Graphiti容器化服务"""
    try:
        data = request.get_json()
        action = data.get('action')

        if action not in ['start', 'stop']:
            return jsonify({
                'success': False,
                'message': '无效的操作类型，支持: start, stop'
            }), 400

        import subprocess
        import os

        # 构建docker-compose命令
        compose_file = '../thirt_party/graphiti/docker-compose-abm.yml'

        if action == 'start':
            cmd = ['docker-compose', '-f', compose_file, 'up', '-d']
        elif action == 'stop':
            cmd = ['docker-compose', '-f', compose_file, 'down']

        # 执行命令
        try:
            # 获取项目根目录
            project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

            # 准备环境变量
            env = os.environ.copy()

            # 如果是启动操作，生成环境变量
            if action == 'start':
                try:
                    env_vars = graph_service.generate_env_vars_for_graphiti()
                    if env_vars:
                        current_app.logger.info(f"传递环境变量到docker-compose:")
                        for key, value in env_vars.items():
                            # 对于敏感信息（API密钥），只显示前几位和后几位
                            if 'API_KEY' in key.upper() or 'PASSWORD' in key.upper():
                                if len(value) > 8:
                                    masked_value = f"{value[:4]}...{value[-4:]}"
                                else:
                                    masked_value = "***"
                                current_app.logger.info(f"  {key}={masked_value}")
                            else:
                                current_app.logger.info(f"  {key}={value}")
                        env.update(env_vars)
                except Exception as e:
                    current_app.logger.error(f"生成环境变量失败: {e}")
                    return jsonify({
                        'success': False,
                        'message': f'生成环境变量失败: {str(e)}'
                    }), 500

            result = subprocess.run(
                cmd,
                cwd=project_root,
                capture_output=True,
                text=True,
                timeout=60,
                env=env
            )

            if result.returncode == 0:
                action_text = {'start': '启动', 'stop': '停止', 'restart': '重启'}[action]
                return jsonify({
                    'success': True,
                    'message': f'Graphiti服务{action_text}成功',
                    'output': result.stdout
                })
            else:
                return jsonify({
                    'success': False,
                    'message': f'服务操作失败: {result.stderr}',
                    'output': result.stderr
                }), 500

        except subprocess.TimeoutExpired:
            return jsonify({
                'success': False,
                'message': '操作超时，请检查Docker服务状态'
            }), 500
        except FileNotFoundError:
            return jsonify({
                'success': False,
                'message': 'docker-compose命令未找到，请确保Docker已安装'
            }), 500

    except Exception as e:
        current_app.logger.error(f"控制Graphiti服务失败: {e}")
        return jsonify({
            'success': False,
            'message': f'服务控制失败: {str(e)}'
        }), 500
