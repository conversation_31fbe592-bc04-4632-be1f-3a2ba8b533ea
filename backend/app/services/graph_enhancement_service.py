"""
图谱增强服务

提供图谱增强功能的核心服务，包括：
- 框架初始化和管理
- 查询处理
- 状态监控
- 数据管理
"""

import os
import json
import asyncio
from datetime import datetime
from typing import Dict, Any, Tuple, Optional
from flask import current_app

class MockLightRAG:
    """LightRAG模拟类，用于在未安装LightRAG时提供基本功能"""

    def __init__(self, config):
        self.config = config
        self.working_dir = config.working_dir or f"./graph_storage/lightrag_{config.id}"
        self.documents = []
        self.entities = []
        self.relations = []

    async def ainsert(self, text):
        """模拟插入文档"""
        self.documents.append({
            'text': text,
            'timestamp': datetime.now().isoformat()
        })
        return True

    async def aquery(self, query, param=None):
        """模拟查询"""
        return f"模拟查询结果：针对查询 '{query}' 的回答。这是一个模拟响应，请安装lightrag-hku以获得真实的图谱增强功能。"

    def get_stats(self):
        """获取统计信息"""
        return {
            'entity_count': len(self.entities),
            'relation_count': len(self.relations),
            'document_count': len(self.documents)
        }

class GraphEnhancementService:
    """图谱增强服务类"""
    
    def __init__(self):
        self.frameworks = {}  # 存储已初始化的框架实例
        self.supported_frameworks = ['lightrag', 'graphiti', 'graphrag']
    
    def initialize_framework(self, config) -> Tuple[bool, str]:
        """初始化图谱增强框架"""
        try:
            framework = config.framework
            if framework not in self.supported_frameworks:
                return False, f"不支持的框架: {framework}"
            
            # 根据框架类型初始化
            if framework == 'lightrag':
                return self._initialize_lightrag(config)
            elif framework == 'graphiti':
                return self._initialize_graphiti(config)
            elif framework == 'graphrag':
                return self._initialize_graphrag(config)
            else:
                return False, f"框架 {framework} 尚未实现"
                
        except Exception as e:
            current_app.logger.error(f"初始化图谱增强框架失败: {e}")
            return False, f"初始化失败: {str(e)}"
    
    def _initialize_lightrag(self, config) -> Tuple[bool, str]:
        """初始化LightRAG框架"""
        try:
            # 检查LightRAG是否已安装
            try:
                import lightrag
                from lightrag import LightRAG, QueryParam
                from lightrag.llm.openai import gpt_4o_mini_complete, openai_embed
                from lightrag.utils import EmbeddingFunc
            except ImportError:
                # 如果LightRAG未安装，创建一个模拟实例用于测试
                current_app.logger.warning("LightRAG未安装，使用模拟实例")
                self.frameworks[config.id] = {
                    'type': 'lightrag',
                    'instance': MockLightRAG(config),
                    'config': config,
                    'initialized_at': datetime.now()
                }
                return True, "LightRAG模拟实例初始化成功（请安装lightrag-hku以使用完整功能）"

            # 获取配置参数
            framework_config = config.framework_config or {}
            working_dir = config.working_dir or f"./graph_storage/lightrag_{config.id}"

            # 确保工作目录存在
            os.makedirs(working_dir, exist_ok=True)

            # 创建LightRAG实例
            rag_config = {
                'working_dir': working_dir,
                'chunk_token_size': framework_config.get('chunk_token_size', 1200),
                'chunk_overlap_token_size': framework_config.get('chunk_overlap_token_size', 100),
                'enable_llm_cache': framework_config.get('enable_llm_cache', True),
                'enable_llm_cache_for_entity_extract': framework_config.get('enable_entity_cache', True)
            }

            # 配置LLM和嵌入模型
            if config.llm_config == 'inherit':
                # 使用系统配置的默认文本生成模型
                rag_config['llm_model_func'] = self._get_default_text_model_func()
            else:
                # 使用自定义配置
                # TODO: 实现自定义LLM配置
                rag_config['llm_model_func'] = self._get_default_text_model_func()

            if config.embedding_config == 'inherit':
                # 使用系统配置的默认嵌入模型
                rag_config['embedding_func'] = self._get_default_embedding_func()
            else:
                # 使用自定义配置
                # TODO: 实现自定义嵌入配置
                rag_config['embedding_func'] = self._get_default_embedding_func()

            # 创建LightRAG实例
            rag = LightRAG(**rag_config)

            # 异步初始化
            async def init_rag():
                await rag.initialize_storages()
                from lightrag.kg.shared_storage import initialize_pipeline_status
                await initialize_pipeline_status()
                return rag

            # 运行异步初始化
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                rag_instance = loop.run_until_complete(init_rag())
                self.frameworks[config.id] = {
                    'type': 'lightrag',
                    'instance': rag_instance,
                    'config': config,
                    'initialized_at': datetime.now()
                }
                return True, "LightRAG初始化成功"
            finally:
                loop.close()

        except Exception as e:
            current_app.logger.error(f"初始化LightRAG失败: {e}")
            return False, f"初始化LightRAG失败: {str(e)}"
    
    def _initialize_graphiti(self, config) -> Tuple[bool, str]:
        """初始化Graphiti框架 - 容器化版本（无需实际初始化）"""
        try:
            # 获取配置参数
            framework_config = config.framework_config or {}

            # 获取容器化服务的配置
            graphiti_service_url = framework_config.get('service_url', 'http://localhost:8000')

            # 直接存储框架信息，不进行连接测试（延迟到实际使用时）
            self.frameworks[config.id] = {
                'type': 'graphiti',
                'service_url': graphiti_service_url,
                'config': config,
                'framework_config': framework_config,
                'initialized_at': datetime.now()
            }

            current_app.logger.info(f"Graphiti容器化服务配置完成，服务地址: {graphiti_service_url}")
            return True, "Graphiti容器化服务配置成功"

        except Exception as e:
            current_app.logger.error(f"配置Graphiti容器化服务失败: {e}")
            return False, f"配置Graphiti容器化服务失败: {str(e)}"

    def _test_graphiti_service_connection(self, service_url: str) -> Tuple[bool, str]:
        """测试Graphiti容器化服务连接"""
        try:
            import requests

            # 测试健康检查端点
            health_url = f"{service_url.rstrip('/')}/healthcheck"

            try:
                response = requests.get(health_url, timeout=10)
                if response.status_code == 200:
                    # 解析健康检查响应
                    try:
                        health_data = response.json()
                        status = health_data.get('status', 'unknown')
                        if status == 'healthy':
                            return True, "Graphiti容器化服务运行正常"
                        else:
                            return False, f"服务状态异常: {status}"
                    except ValueError:
                        # 如果不是JSON响应，检查文本内容
                        if 'healthy' in response.text.lower():
                            return True, "Graphiti容器化服务运行正常"
                        else:
                            return False, f"健康检查响应异常: {response.text[:100]}"
                else:
                    return False, f"健康检查失败，状态码: {response.status_code}"
            except requests.exceptions.ConnectionError:
                return False, f"无法连接到服务地址: {service_url}"
            except requests.exceptions.Timeout:
                return False, "连接超时，请检查服务是否正常运行"
            except Exception as e:
                return False, f"连接测试失败: {str(e)}"

        except ImportError:
            return False, "缺少requests库，无法测试HTTP连接"
        except Exception as e:
            return False, f"测试连接时发生错误: {str(e)}"
    
    def _initialize_graphrag(self, config) -> Tuple[bool, str]:
        """初始化GraphRAG框架"""
        try:
            # TODO: 实现GraphRAG初始化
            return False, "GraphRAG框架尚未实现"
            
        except Exception as e:
            current_app.logger.error(f"初始化GraphRAG失败: {e}")
            return False, f"初始化GraphRAG失败: {str(e)}"
    
    def get_status(self, config) -> Dict[str, Any]:
        """获取图谱增强状态"""
        try:
            # 对于graphiti，直接使用配置获取状态，无需初始化
            if config.framework == 'graphiti':
                return self._get_graphiti_status_direct(config)

            # 其他框架保持原有逻辑
            framework_info = self.frameworks.get(config.id)
            if not framework_info:
                # 框架未初始化，尝试自动初始化
                current_app.logger.info(f"框架未初始化，尝试自动初始化: {config.framework}")
                success, message = self.initialize_framework(config)
                if not success:
                    return {
                        'enabled': config.enabled,
                        'framework': config.framework,
                        'status': 'initialization_failed',
                        'message': f'自动初始化失败: {message}'
                    }
                framework_info = self.frameworks.get(config.id)
                if not framework_info:
                    return {
                        'enabled': config.enabled,
                        'framework': config.framework,
                        'status': 'not_initialized',
                        'message': '自动初始化后仍无法获取框架实例'
                    }

            # 根据框架类型获取状态
            if framework_info['type'] == 'lightrag':
                return self._get_lightrag_status(framework_info)
            elif framework_info['type'] == 'graphiti':
                return self._get_graphiti_status(framework_info)
            elif framework_info['type'] == 'graphrag':
                return self._get_graphrag_status(framework_info)
            else:
                return {
                    'enabled': config.enabled,
                    'framework': config.framework,
                    'status': 'unknown',
                    'message': '未知框架类型'
                }

        except Exception as e:
            current_app.logger.error(f"获取图谱增强状态失败: {e}")
            return {
                'enabled': False,
                'status': 'error',
                'message': f'获取状态失败: {str(e)}'
            }
    
    def _get_lightrag_status(self, framework_info) -> Dict[str, Any]:
        """获取LightRAG状态"""
        try:
            rag = framework_info['instance']
            config = framework_info['config']
            
            # 获取统计信息
            working_dir = config.working_dir or f"./graph_storage/lightrag_{config.id}"
            
            # 统计实体和关系数量
            entity_count = 0
            relation_count = 0
            
            # TODO: 从LightRAG实例获取实际统计信息
            
            return {
                'enabled': True,
                'framework': 'lightrag',
                'status': 'ready',
                'connected': True,
                'indexed': True,
                'statistics': {
                    'entity_count': entity_count,
                    'relation_count': relation_count,
                    'document_count': 0,  # TODO: 获取实际文档数量
                    'last_update': framework_info['initialized_at'].isoformat()
                },
                'working_dir': working_dir
            }
            
        except Exception as e:
            return {
                'enabled': True,
                'framework': 'lightrag',
                'status': 'error',
                'message': f'获取LightRAG状态失败: {str(e)}'
            }
    
    def _get_graphiti_status(self, framework_info) -> Dict[str, Any]:
        """获取Graphiti状态 - 容器化版本"""
        try:
            service_url = framework_info['service_url']
            config = framework_info['config']

            # 测试容器化服务连接
            success, message = self._test_graphiti_service_connection(service_url)

            if success:
                return {
                    'enabled': True,
                    'framework': 'graphiti',
                    'status': 'ready',
                    'connected': True,
                    'indexed': True,
                    'service_url': service_url,
                    'statistics': {
                        'entity_count': 0,
                        'relation_count': 0,
                        'episode_count': 0,
                        'last_update': framework_info['initialized_at'].isoformat()
                    }
                }
            else:
                return {
                    'enabled': True,
                    'framework': 'graphiti',
                    'status': 'disconnected',
                    'connected': False,
                    'indexed': False,
                    'service_url': service_url,
                    'message': f'容器化服务连接失败: {message}'
                }

        except Exception as e:
            return {
                'enabled': True,
                'framework': 'graphiti',
                'status': 'error',
                'message': f'获取Graphiti状态失败: {str(e)}'
            }

    def _get_graphiti_status_direct(self, config) -> Dict[str, Any]:
        """直接获取Graphiti状态 - 使用配置中的服务地址"""
        try:
            framework_config = config.framework_config or {}
            service_url = framework_config.get('service_url', 'http://localhost:8000')

            # 测试容器化服务连接
            success, message = self._test_graphiti_service_connection(service_url)

            if success:
                return {
                    'enabled': True,
                    'framework': 'graphiti',
                    'status': 'ready',
                    'connected': True,
                    'indexed': True,
                    'service_url': service_url,
                    'statistics': {
                        'entity_count': 0,
                        'relation_count': 0,
                        'episode_count': 0,
                        'last_update': datetime.now().isoformat()
                    }
                }
            else:
                return {
                    'enabled': True,
                    'framework': 'graphiti',
                    'status': 'disconnected',
                    'connected': False,
                    'indexed': False,
                    'service_url': service_url,
                    'message': f'容器化服务连接失败: {message}'
                }

        except Exception as e:
            return {
                'enabled': True,
                'framework': 'graphiti',
                'status': 'error',
                'message': f'获取Graphiti状态失败: {str(e)}'
            }

    def _get_graphrag_status(self, framework_info) -> Dict[str, Any]:
        """获取GraphRAG状态"""
        # TODO: 实现GraphRAG状态获取
        return {
            'enabled': True,
            'framework': 'graphrag',
            'status': 'not_implemented',
            'message': 'GraphRAG状态获取尚未实现'
        }
    
    def query(self, config, query: str, params: Dict[str, Any]) -> Tuple[bool, Any]:
        """执行图谱增强查询"""
        try:
            # 对于graphiti，直接使用配置进行查询，无需初始化
            if config.framework == 'graphiti':
                return self._query_graphiti_direct(config, query, params)

            # 其他框架保持原有逻辑
            framework_info = self.frameworks.get(config.id)
            if not framework_info:
                # 框架未初始化，尝试自动初始化
                current_app.logger.info(f"框架未初始化，尝试自动初始化: {config.framework}")
                success, message = self.initialize_framework(config)
                if not success:
                    return False, f"自动初始化框架失败: {message}"
                framework_info = self.frameworks.get(config.id)
                if not framework_info:
                    return False, "自动初始化后仍无法获取框架实例"

            # 根据框架类型执行查询
            if framework_info['type'] == 'lightrag':
                return self._query_lightrag(framework_info, query, params)
            elif framework_info['type'] == 'graphiti':
                return self._query_graphiti(framework_info, query, params)
            elif framework_info['type'] == 'graphrag':
                return self._query_graphrag(framework_info, query, params)
            else:
                return False, f"不支持的框架类型: {framework_info['type']}"

        except Exception as e:
            current_app.logger.error(f"执行图谱增强查询失败: {e}")
            return False, f"查询失败: {str(e)}"

    def query_advanced(self, config, query: str, params: Dict[str, Any]) -> Tuple[bool, Any]:
        """执行高级图谱增强查询"""
        try:
            # 对于graphiti，使用高级搜索配置
            if config.framework == 'graphiti':
                return self._query_graphiti_advanced(config, query, params)

            # 其他框架暂时使用普通查询
            return self.query(config, query, params)

        except Exception as e:
            current_app.logger.error(f"执行高级图谱增强查询失败: {e}")
            return False, f"高级查询失败: {str(e)}"
    
    def _query_lightrag(self, framework_info, query: str, params: Dict[str, Any]) -> Tuple[bool, Any]:
        """执行LightRAG查询"""
        try:
            rag = framework_info['instance']
            
            # 构建查询参数
            from lightrag import QueryParam
            query_param = QueryParam(
                mode=params.get('mode', 'hybrid'),
                top_k=params.get('top_k', 60),
                chunk_top_k=params.get('chunk_top_k', 10),
                response_type=params.get('response_type', 'Multiple Paragraphs')
            )
            
            # 执行异步查询
            async def run_query():
                return await rag.aquery(query, param=query_param)
            
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                result = loop.run_until_complete(run_query())
                return True, result
            finally:
                loop.close()
                
        except Exception as e:
            current_app.logger.error(f"LightRAG查询失败: {e}")
            return False, f"LightRAG查询失败: {str(e)}"
    
    def _query_graphiti(self, framework_info, query: str, params: Dict[str, Any]) -> Tuple[bool, Any]:
        """执行Graphiti查询 - 容器化版本"""
        try:
            import requests

            service_url = framework_info['service_url']

            # 构建查询请求 - 使用/search端点
            query_url = f"{service_url.rstrip('/')}/search"

            # 准备查询参数
            query_data = {
                'query': query
            }

            # 发送查询请求
            try:
                response = requests.post(
                    query_url,
                    json=query_data,
                    timeout=30,
                    headers={'Content-Type': 'application/json'}
                )

                if response.status_code == 200:
                    result = response.json()

                    # 格式化为结构化结果，与高级搜索保持一致
                    if 'facts' in result:
                        facts = result['facts']
                        if facts:
                            # 返回结构化的facts数据
                            return True, {
                                'type': 'facts',
                                'facts': facts,
                                'total_count': len(facts),
                                'raw_result': result
                            }
                        else:
                            return True, {
                                'type': 'empty',
                                'message': "未找到相关信息",
                                'facts': [],
                                'total_count': 0
                            }
                    else:
                        return True, {
                            'type': 'raw',
                            'message': str(result),
                            'facts': [],
                            'total_count': 0
                        }
                else:
                    error_msg = f"查询失败，状态码: {response.status_code}"
                    try:
                        error_detail = response.json()
                        if 'error' in error_detail:
                            error_msg += f", 错误: {error_detail['error']}"
                    except:
                        error_msg += f", 响应: {response.text[:200]}"
                    return False, error_msg

            except requests.exceptions.ConnectionError:
                return False, f"无法连接到Graphiti服务: {service_url}"
            except requests.exceptions.Timeout:
                return False, "查询超时，请稍后重试"
            except Exception as e:
                return False, f"查询请求失败: {str(e)}"

        except ImportError:
            return False, "缺少requests库，无法执行HTTP查询"
        except Exception as e:
            current_app.logger.error(f"Graphiti查询失败: {e}")
            return False, f"Graphiti查询失败: {str(e)}"

    def _query_graphiti_direct(self, config, query: str, params: Dict[str, Any]) -> Tuple[bool, Any]:
        """直接执行Graphiti查询 - 使用配置中的服务地址"""
        try:
            import requests

            framework_config = config.framework_config or {}
            service_url = framework_config.get('service_url', 'http://localhost:8000')

            # 构建查询请求 - 使用/search端点
            query_url = f"{service_url.rstrip('/')}/search"

            # 准备查询参数，支持Graphiti的完整参数
            query_data = {
                'query': query,
                'max_facts': params.get('max_facts', 10)
            }

            # 处理group_ids参数
            group_ids = params.get('group_ids', [])
            if group_ids and len(group_ids) > 0:
                # 过滤掉空字符串
                group_ids = [gid.strip() for gid in group_ids if gid and gid.strip()]
                if group_ids:
                    query_data['group_ids'] = group_ids
                else:
                    query_data['group_ids'] = None
            else:
                query_data['group_ids'] = None

            current_app.logger.info(f"调用Graphiti搜索API: {query_url}")
            current_app.logger.info(f"请求数据: {query_data}")

            # 发送查询请求
            try:
                response = requests.post(
                    query_url,
                    json=query_data,
                    timeout=30,
                    headers={'Content-Type': 'application/json'}
                )

                current_app.logger.info(f"Graphiti响应状态: {response.status_code}")

                if response.status_code == 200:
                    result = response.json()
                    current_app.logger.info(f"Graphiti响应数据: {result}")

                    # 格式化为结构化结果，与高级搜索保持一致
                    if 'facts' in result:
                        facts = result['facts']
                        if facts:
                            # 返回结构化的facts数据
                            return True, {
                                'type': 'facts',
                                'facts': facts,
                                'total_count': len(facts),
                                'raw_result': result
                            }
                        else:
                            return True, {
                                'type': 'empty',
                                'message': "未找到相关信息",
                                'facts': [],
                                'total_count': 0
                            }
                    else:
                        return True, {
                            'type': 'raw',
                            'message': str(result),
                            'facts': [],
                            'total_count': 0
                        }
                else:
                    error_msg = f"查询失败，状态码: {response.status_code}"
                    try:
                        error_detail = response.json()
                        if 'error' in error_detail:
                            error_msg += f", 错误: {error_detail['error']}"
                        elif 'detail' in error_detail:
                            error_msg += f", 详情: {error_detail['detail']}"
                    except:
                        error_msg += f", 响应: {response.text[:200]}"
                    current_app.logger.error(error_msg)
                    return False, error_msg

            except requests.exceptions.ConnectionError:
                return False, f"无法连接到Graphiti服务: {service_url}"
            except requests.exceptions.Timeout:
                return False, "查询超时，请稍后重试"
            except Exception as e:
                return False, f"查询请求失败: {str(e)}"

        except ImportError:
            return False, "缺少requests库，无法执行HTTP查询"
        except Exception as e:
            current_app.logger.error(f"Graphiti查询失败: {e}")
            return False, f"Graphiti查询失败: {str(e)}"

    def _query_graphiti_advanced(self, config, query: str, params: Dict[str, Any]) -> Tuple[bool, Any]:
        """执行Graphiti高级查询 - 使用高级搜索配置"""
        try:
            import requests

            framework_config = config.framework_config or {}
            service_url = framework_config.get('service_url', 'http://localhost:8000')

            # 构建高级查询请求 - 使用/search_advanced端点
            query_url = f"{service_url.rstrip('/')}/search_advanced"

            # 准备高级查询参数
            query_data = {
                'query': query,
                'search_mode': params.get('search_mode', 'cross_encoder'),
                'max_facts': params.get('max_facts', 15),
                'reranker_min_score': params.get('reranker_min_score', 0.6),
                'sim_min_score': params.get('sim_min_score', 0.5),
                'enable_filters': params.get('enable_filters', False)
            }

            # 处理group_ids参数
            group_ids = params.get('group_ids', [])
            if group_ids and len(group_ids) > 0:
                # 过滤掉空字符串
                group_ids = [gid.strip() for gid in group_ids if gid and gid.strip()]
                if group_ids:
                    query_data['group_ids'] = group_ids
                else:
                    query_data['group_ids'] = None
            else:
                query_data['group_ids'] = None

            # 处理过滤器参数
            if params.get('enable_filters', False):
                query_data['node_labels'] = params.get('node_labels', [])
                query_data['edge_types'] = params.get('edge_types', [])

            current_app.logger.info(f"调用Graphiti高级搜索API: {query_url}")
            current_app.logger.info(f"请求数据: {query_data}")

            # 发送查询请求
            try:
                response = requests.post(
                    query_url,
                    json=query_data,
                    timeout=30,
                    headers={'Content-Type': 'application/json'}
                )

                current_app.logger.info(f"Graphiti高级搜索响应状态: {response.status_code}")

                if response.status_code == 200:
                    result = response.json()
                    current_app.logger.info(f"Graphiti高级搜索响应数据: {result}")

                    # 返回结构化结果
                    if 'facts' in result:
                        facts = result['facts']
                        if facts:
                            # 返回结构化的facts数据
                            return True, {
                                'type': 'facts',
                                'facts': facts,
                                'total_count': len(facts),
                                'raw_result': result
                            }
                        else:
                            return True, {
                                'type': 'empty',
                                'message': "未找到相关信息",
                                'facts': [],
                                'total_count': 0
                            }
                    else:
                        return True, {
                            'type': 'raw',
                            'message': str(result),
                            'facts': [],
                            'total_count': 0
                        }
                else:
                    error_msg = f"高级查询失败，状态码: {response.status_code}"
                    try:
                        error_detail = response.json()
                        if 'error' in error_detail:
                            error_msg += f", 错误: {error_detail['error']}"
                        elif 'detail' in error_detail:
                            error_msg += f", 详情: {error_detail['detail']}"
                    except:
                        error_msg += f", 响应: {response.text[:200]}"
                    current_app.logger.error(error_msg)
                    return False, error_msg

            except requests.exceptions.ConnectionError:
                return False, f"无法连接到Graphiti服务: {service_url}"
            except requests.exceptions.Timeout:
                return False, "高级查询超时，请稍后重试"
            except Exception as e:
                return False, f"高级查询请求失败: {str(e)}"

        except ImportError:
            return False, "缺少requests库，无法执行HTTP高级查询"
        except Exception as e:
            current_app.logger.error(f"Graphiti高级查询失败: {e}")
            return False, f"Graphiti高级查询失败: {str(e)}"

    def _query_graphrag(self, framework_info, query: str, params: Dict[str, Any]) -> Tuple[bool, Any]:
        """执行GraphRAG查询"""
        try:
            # TODO: 实现GraphRAG查询
            return False, "GraphRAG查询尚未实现"
            
        except Exception as e:
            current_app.logger.error(f"GraphRAG查询失败: {e}")
            return False, f"GraphRAG查询失败: {str(e)}"
    

    
    def rebuild_index(self, config) -> Tuple[bool, str]:
        """重建图谱增强索引 - 容器化版本不支持此功能"""
        return False, "容器化版本的Graphiti不支持重建索引功能"
    
    def clear_data(self, config) -> Tuple[bool, str]:
        """清空图谱增强数据"""
        try:
            # 对于graphiti，直接使用配置进行清空，无需初始化
            if config.framework == 'graphiti':
                return self._clear_data_graphiti_direct(config)

            # 其他框架保持原有逻辑
            framework_info = self.frameworks.get(config.id)
            if not framework_info:
                # 框架未初始化，尝试自动初始化
                current_app.logger.info(f"框架未初始化，尝试自动初始化: {config.framework}")
                success, message = self.initialize_framework(config)
                if not success:
                    return False, f"自动初始化框架失败: {message}"
                framework_info = self.frameworks.get(config.id)
                if not framework_info:
                    return False, "自动初始化后仍无法获取框架实例"

            # 根据框架类型清空数据
            if framework_info['type'] == 'lightrag':
                return self._clear_data_lightrag(framework_info)
            elif framework_info['type'] == 'graphiti':
                # 对于graphiti，使用直接方法
                return self._clear_data_graphiti_direct(config)
            elif framework_info['type'] == 'graphrag':
                return self._clear_data_graphrag(framework_info)
            else:
                return False, f"不支持的框架类型: {framework_info['type']}"

        except Exception as e:
            current_app.logger.error(f"清空图谱增强数据失败: {e}")
            return False, f"清空数据失败: {str(e)}"

    def add_documents(self, config, documents: list) -> Tuple[bool, str]:
        """添加文档到图谱增强系统"""
        try:
            framework_info = self.frameworks.get(config.id)
            if not framework_info:
                return False, "框架未初始化"

            # 根据框架类型添加文档
            if framework_info['type'] == 'lightrag':
                return self._add_documents_lightrag(framework_info, documents)
            elif framework_info['type'] == 'graphiti':
                return self._add_documents_graphiti(framework_info, documents)
            elif framework_info['type'] == 'graphrag':
                return self._add_documents_graphrag(framework_info, documents)
            else:
                return False, f"不支持的框架类型: {framework_info['type']}"

        except Exception as e:
            current_app.logger.error(f"添加文档失败: {e}")
            return False, f"添加文档失败: {str(e)}"

    def _clear_data_lightrag(self, framework_info) -> Tuple[bool, str]:
        """清空LightRAG数据"""
        try:
            rag = framework_info['instance']
            config = framework_info['config']

            # 如果是模拟实例
            if isinstance(rag, MockLightRAG):
                rag.documents.clear()
                rag.entities.clear()
                rag.relations.clear()
                return True, "LightRAG模拟数据清空成功"

            # 对于真实的LightRAG实例，清空工作目录
            working_dir = config.working_dir or f"./graph_storage/lightrag_{config.id}"
            if os.path.exists(working_dir):
                import shutil
                shutil.rmtree(working_dir)
                os.makedirs(working_dir, exist_ok=True)
                current_app.logger.info(f"LightRAG工作目录已清空: {working_dir}")

            return True, "LightRAG数据清空成功"

        except Exception as e:
            current_app.logger.error(f"清空LightRAG数据失败: {e}")
            return False, f"清空LightRAG数据失败: {str(e)}"



    def _clear_data_graphiti_direct(self, config) -> Tuple[bool, str]:
        """直接清空Graphiti数据 - 使用配置中的服务地址"""
        try:
            import requests

            framework_config = config.framework_config or {}
            service_url = framework_config.get('service_url', 'http://localhost:8000')

            # 构建清空数据请求
            clear_url = f"{service_url.rstrip('/')}/clear"

            # 发送清空数据请求（不等待结果）
            try:
                response = requests.post(
                    clear_url,
                    timeout=5,  # 短超时，不等待完成
                    headers={'Content-Type': 'application/json'}
                )

                if response.status_code == 200:
                    current_app.logger.info("Graphiti清空数据请求已发送")
                    return True, "清空数据请求已发送，正在后台处理"
                else:
                    current_app.logger.warning(f"清空数据请求返回状态码: {response.status_code}")
                    return True, "清空数据请求已发送"  # 即使状态码不是200也认为成功发送

            except requests.exceptions.Timeout:
                # 超时也认为是成功的，因为我们不等待结果
                current_app.logger.info("清空数据请求超时，但请求已发送")
                return True, "清空数据请求已发送，正在后台处理"
            except requests.exceptions.ConnectionError:
                return False, f"无法连接到Graphiti服务: {service_url}"
            except Exception as e:
                return False, f"清空数据请求失败: {str(e)}"

        except ImportError:
            return False, "缺少requests库，无法执行HTTP请求"
        except Exception as e:
            current_app.logger.error(f"清空Graphiti数据失败: {e}")
            return False, f"清空Graphiti数据失败: {str(e)}"

    def generate_env_vars_for_graphiti(self) -> Dict[str, str]:
        """为Graphiti容器化服务生成环境变量"""
        try:
            from app.models import GraphEnhancement

            # 获取图谱增强配置（通常只有一个配置）
            config = GraphEnhancement.query.first()
            if not config:
                raise Exception("未找到图谱增强配置")

            current_app.logger.info(f"使用配置: ID={config.id}, Framework={config.framework}, Name={config.name}")

            framework_config = config.framework_config or {}
            current_app.logger.info(f"读取到的Framework配置: {framework_config}")
            env_vars = {}

            # 检查配置是否为空
            if not framework_config:
                current_app.logger.warning("Framework配置为空，可能配置没有正确保存")
                raise Exception("图谱增强配置为空，请先在界面中配置并保存")

            # 处理文本生成模型 - 从framework_config中的text_model读取
            text_model_config = framework_config.get('text_model')
            current_app.logger.info(f"文本模型配置: {text_model_config}")

            if text_model_config:
                # 直接从保存的配置中读取
                env_vars["OPENAI_API_KEY"] = text_model_config.get('api_key', 'no-api-key')
                env_vars["MODEL_NAME"] = text_model_config.get('model_id', '')
                env_vars["SMALL_MODEL_NAME"] = text_model_config.get('model_id', '')

                # 设置base_url
                if text_model_config.get('base_url'):
                    env_vars["OPENAI_BASE_URL"] = text_model_config['base_url']

                current_app.logger.info(f"使用文本模型: {text_model_config.get('name', 'Unknown')}")
            else:
                raise Exception("未找到文本生成模型配置，请在界面中选择并保存")

            # 处理嵌入模型 - 从framework_config中的embedding_model读取
            embedding_model_config = framework_config.get('embedding_model')
            current_app.logger.info(f"嵌入模型配置: {embedding_model_config}")

            if embedding_model_config:
                # 直接从保存的配置中读取
                env_vars["OPENAI_EMBEDDER_API_KEY"] = embedding_model_config.get('api_key', 'no-api-key')
                env_vars["OPENAI_EMBEDDER_MODEL_ID"] = embedding_model_config.get('model_id', '')

                # 设置维度 - 优先使用用户在界面中配置的维度
                if framework_config.get('embedding_dimension'):
                    env_vars["OPENAI_EMBEDDER_DIMENSION"] = str(framework_config['embedding_dimension'])
                else:
                    # 如果用户没有配置维度，使用默认值1024
                    env_vars["OPENAI_EMBEDDER_DIMENSION"] = "1024"

                # 设置base_url
                if embedding_model_config.get('base_url'):
                    env_vars["OPENAI_EMBEDDER_API_URL"] = embedding_model_config['base_url']
                elif env_vars.get("OPENAI_BASE_URL"):
                    env_vars["OPENAI_EMBEDDER_API_URL"] = env_vars["OPENAI_BASE_URL"]

                current_app.logger.info(f"使用嵌入模型: {embedding_model_config.get('name', 'Unknown')}")
            else:
                raise Exception("未找到嵌入模型配置，请在界面中选择并保存")

            # 处理重排序模型 - 从framework_config中的rerank_model读取
            rerank_model_config = framework_config.get('rerank_model')
            rerank_type = framework_config.get('rerank_type', 'reranker')  # 默认为标准重排序
            current_app.logger.info(f"重排序模型配置: {rerank_model_config}")
            current_app.logger.info(f"重排序类型: {rerank_type}")

            # 设置重排序类型环境变量（直接使用前端的值）
            env_vars["RERANKER_TYPE"] = rerank_type

            if rerank_model_config:
                # 直接从保存的配置中读取
                env_vars["RERANK_MODEL_API_KEY"] = rerank_model_config.get('api_key', 'no-api-key')
                env_vars["RERANK_MODEL_ID"] = rerank_model_config.get('model_id', '')

                # 设置base_url
                if rerank_model_config.get('base_url'):
                    env_vars["RERANK_MODEL_API_URL"] = rerank_model_config['base_url']
                elif env_vars.get("OPENAI_BASE_URL"):
                    env_vars["RERANK_MODEL_API_URL"] = env_vars["OPENAI_BASE_URL"]

                current_app.logger.info(f"使用重排序模型: {rerank_model_config.get('name', 'Unknown')}")
                current_app.logger.info(f"重排序模型环境变量 - Type: {env_vars.get('RERANKER_TYPE')}, ID: {env_vars.get('RERANK_MODEL_ID')}, API_KEY: {'已设置' if env_vars.get('RERANK_MODEL_API_KEY') else '未设置'}, API_URL: {env_vars.get('RERANK_MODEL_API_URL')}")
            else:
                # 重排序模型是可选的，如果没有配置则不设置重排序模型环境变量
                current_app.logger.info("未配置重排序模型，跳过重排序模型设置")
                current_app.logger.info(f"重排序类型环境变量: {env_vars.get('RERANKER_TYPE')}")

            # 添加其他配置
            if framework_config.get('group_id'):
                env_vars["GROUP_ID"] = framework_config['group_id']

            # 固定设置并发限制为10
            env_vars["SEMAPHORE_LIMIT"] = "10"

            # 添加OpenAI兼容标记
            if framework_config.get('openai_compatible'):
                env_vars["OPENAI_COMPATIBLE"] = "true"
            else:
                env_vars["OPENAI_COMPATIBLE"] = "false"

            return env_vars

        except Exception as e:
            current_app.logger.error(f"生成Graphiti环境变量失败: {e}")
            raise e

    def _clear_data_graphrag(self, framework_info) -> Tuple[bool, str]:
        """清空GraphRAG数据"""
        try:
            # GraphRAG清空逻辑（待实现）
            return False, "GraphRAG数据清空功能尚未实现"

        except Exception as e:
            current_app.logger.error(f"清空GraphRAG数据失败: {e}")
            return False, f"清空GraphRAG数据失败: {str(e)}"



    def _add_documents_lightrag(self, framework_info, documents: list) -> Tuple[bool, str]:
        """向LightRAG添加文档"""
        try:
            rag = framework_info['instance']

            # 合并所有文档内容
            combined_text = "\n\n".join(documents)

            # 异步插入文档
            async def insert_docs():
                await rag.ainsert(combined_text)

            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                loop.run_until_complete(insert_docs())
                return True, f"成功添加 {len(documents)} 个文档到LightRAG"
            finally:
                loop.close()

        except Exception as e:
            current_app.logger.error(f"LightRAG添加文档失败: {e}")
            return False, f"LightRAG添加文档失败: {str(e)}"

    def _add_documents_graphiti(self, framework_info, documents: list) -> Tuple[bool, str]:
        """向Graphiti添加文档 - 容器化版本"""
        try:
            import requests

            service_url = framework_info['service_url']

            # 构建添加文档请求 - 使用/messages端点
            add_docs_url = f"{service_url.rstrip('/')}/messages"

            # 准备文档数据 - 将文档转换为消息格式
            messages_data = []
            for doc in documents:
                if isinstance(doc, str):
                    messages_data.append({'content': doc})
                elif isinstance(doc, dict):
                    messages_data.append(doc)
                else:
                    messages_data.append({'content': str(doc)})

            docs_data = {
                'messages': messages_data
            }

            # 发送添加文档请求
            try:
                response = requests.post(
                    add_docs_url,
                    json=docs_data,
                    timeout=60,  # 添加文档可能需要更长时间
                    headers={'Content-Type': 'application/json'}
                )

                if response.status_code == 200:
                    result = response.json()
                    success_count = result.get('success_count', len(documents))
                    return True, f"成功添加 {success_count} 个文档到Graphiti"
                else:
                    error_msg = f"添加文档失败，状态码: {response.status_code}"
                    try:
                        error_detail = response.json()
                        if 'error' in error_detail:
                            error_msg += f", 错误: {error_detail['error']}"
                    except:
                        error_msg += f", 响应: {response.text[:200]}"
                    return False, error_msg

            except requests.exceptions.ConnectionError:
                return False, f"无法连接到Graphiti服务: {service_url}"
            except requests.exceptions.Timeout:
                return False, "添加文档超时，请稍后重试"
            except Exception as e:
                return False, f"添加文档请求失败: {str(e)}"

        except ImportError:
            return False, "缺少requests库，无法执行HTTP请求"
        except Exception as e:
            current_app.logger.error(f"Graphiti添加文档失败: {e}")
            return False, f"Graphiti添加文档失败: {str(e)}"

    def _add_documents_graphrag(self, framework_info, documents: list) -> Tuple[bool, str]:
        """向GraphRAG添加文档"""
        try:
            # TODO: 实现GraphRAG文档添加
            return False, "GraphRAG文档添加尚未实现"

        except Exception as e:
            current_app.logger.error(f"GraphRAG添加文档失败: {e}")
            return False, f"GraphRAG添加文档失败: {str(e)}"

    def _get_default_text_model_func(self):
        """获取默认文本生成模型函数"""
        try:
            from app.models import ModelConfig
            from lightrag.llm import gpt_4o_mini_complete

            # 获取默认文本生成模型
            default_model = ModelConfig.query.filter_by(is_default_text=True).first()
            if not default_model:
                # 如果没有设置默认文本生成模型，查找第一个支持文本输出的模型
                text_models = ModelConfig.query.filter(
                    ModelConfig.modalities.contains('text_output')
                ).all()
                if text_models:
                    default_model = text_models[0]
                else:
                    # 最后回退到第一个可用模型
                    default_model = ModelConfig.query.first()

            if default_model:
                current_app.logger.info(f"图谱增强使用文本生成模型: {default_model.name}")
                # TODO: 根据不同的模型配置返回相应的模型函数
                # 目前先返回默认的gpt_4o_mini_complete，后续可以根据模型类型动态选择
                return gpt_4o_mini_complete
            else:
                current_app.logger.warning("未找到可用的文本生成模型，使用默认模型")
                return gpt_4o_mini_complete

        except Exception as e:
            current_app.logger.error(f"获取默认文本生成模型失败: {e}")
            return gpt_4o_mini_complete

    def _get_default_embedding_func(self):
        """获取默认嵌入模型函数"""
        try:
            from app.models import ModelConfig
            from lightrag.base import EmbeddingFunc
            from lightrag.llm import openai_embed

            # 获取默认嵌入模型
            default_model = ModelConfig.query.filter_by(is_default_embedding=True).first()

            # 如果没有默认嵌入模型，查找第一个支持向量输出的模型
            if not default_model:
                embedding_models = ModelConfig.query.filter(
                    ModelConfig.modalities.contains('vector_output')
                ).all()
                if embedding_models:
                    default_model = embedding_models[0]

            if default_model:
                current_app.logger.info(f"图谱增强使用嵌入模型: {default_model.name}")
                # TODO: 根据不同的嵌入模型配置返回相应的嵌入函数
                # 目前先返回默认的openai_embed，后续可以根据模型类型动态选择
                return EmbeddingFunc(
                    embedding_dim=1536,
                    max_token_size=8192,
                    func=openai_embed
                )
            else:
                current_app.logger.warning("未找到可用的嵌入模型，使用默认嵌入函数")
                return EmbeddingFunc(
                    embedding_dim=1536,
                    max_token_size=8192,
                    func=openai_embed
                )

        except Exception as e:
            current_app.logger.error(f"获取默认嵌入模型失败: {e}")
            return EmbeddingFunc(
                embedding_dim=1536,
                max_token_size=8192,
                func=openai_embed
            )

    def _get_text_model_config(self, framework_config: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """获取文本生成模型配置"""
        try:
            from app.models import ModelConfig

            text_model_type = framework_config.get('text_model_type', 'default')
            text_model_id = framework_config.get('text_model_id')

            current_app.logger.info(f"获取文本模型配置: type={text_model_type}, id={text_model_id}")

            if text_model_type == 'default' or text_model_id == 'default':
                # 使用默认文本生成模型
                model = ModelConfig.query.filter_by(is_default_text=True).first()
                current_app.logger.info(f"查找默认文本生成模型: {model.name if model else 'None'}")
                if not model:
                    # 查找第一个支持文本输出的模型
                    text_models = ModelConfig.query.filter(
                        ModelConfig.modalities.contains('text_output')
                    ).all()
                    current_app.logger.info(f"查找支持文本输出的模型: {len(text_models)} 个")
                    if text_models:
                        model = text_models[0]
                        current_app.logger.info(f"使用第一个文本输出模型: {model.name}")
                    else:
                        model = ModelConfig.query.first()
                        current_app.logger.info(f"使用第一个可用模型: {model.name if model else 'None'}")
            else:
                # 使用指定的模型
                model = ModelConfig.query.get(text_model_id)
                current_app.logger.info(f"使用指定模型: {model.name if model else 'None'}")

            if model:
                config = {
                    'id': model.id,
                    'name': model.name,
                    'provider': model.provider,
                    'model_id': model.model_id,
                    'api_key': model.api_key,
                    'base_url': model.base_url,
                    'context_window': model.context_window,
                    'max_output_tokens': model.max_output_tokens
                }
                current_app.logger.info(f"返回模型配置: {model.name}, provider={model.provider}, has_api_key={bool(model.api_key)}")
                return config

            current_app.logger.warning("未找到任何可用的文本生成模型")
            return None

        except Exception as e:
            current_app.logger.error(f"获取文本生成模型配置失败: {e}")
            return None

    def _get_embedding_model_config(self, framework_config: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """获取嵌入模型配置"""
        try:
            from app.models import ModelConfig

            embedding_model_type = framework_config.get('embedding_model_type', 'default')
            embedding_model_id = framework_config.get('embedding_model_id')

            if embedding_model_type == 'default' or embedding_model_id == 'default':
                # 使用默认嵌入模型
                model = ModelConfig.query.filter_by(is_default_embedding=True).first()
                if not model:
                    # 查找第一个支持向量输出的模型
                    embedding_models = ModelConfig.query.filter(
                        ModelConfig.modalities.contains('vector_output')
                    ).all()
                    if embedding_models:
                        model = embedding_models[0]
            else:
                # 使用指定的模型
                model = ModelConfig.query.get(embedding_model_id)

            if model:
                return {
                    'id': model.id,
                    'name': model.name,
                    'provider': model.provider,
                    'model_id': model.model_id,
                    'api_key': model.api_key,
                    'base_url': model.base_url
                }

            return None

        except Exception as e:
            current_app.logger.error(f"获取嵌入模型配置失败: {e}")
            return None
