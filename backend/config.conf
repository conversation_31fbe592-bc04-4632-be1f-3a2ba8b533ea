[BACKEND_CONFIG]
# 后端服务地址
DEBUG = True
# 是否输出LLM原始响应内容
DEBUG_LLM_RESPONSE = False
# 日志级别: DEBUG, INFO, WARNING, ERROR, CRITICAL
LOG_LEVEL = DEBUG
HOST = localhost
PORT = 8080


[FRONTEND_CONFIG]
# 前端服务地址
HOST = localhost
PORT = 3000


[GRAPHITI_CONFIG]
# Graphiti知识图谱配置
# Neo4j数据库连接配置
NEO4J_URI = bolt://localhost:7687
NEO4J_USER = neo4j
NEO4J_PASSWORD = password

# 或者使用FalkorDB（轻量级选择）
# FALKORDB_HOST = localhost
# FALKORDB_PORT = 6379

# Graphiti性能配置
GRAPHITI_SEMAPHORE_LIMIT = 10
GRAPHITI_USE_PARALLEL_RUNTIME = false
GRAPHITI_TELEMETRY_ENABLED = false
